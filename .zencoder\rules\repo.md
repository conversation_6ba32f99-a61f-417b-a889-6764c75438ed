---
description: Repository Information Overview
alwaysApply: true
---

# n8n Recruiting Workflow Information

## Summary
This repository contains an n8n workflow designed for recruiting and resume screening. The workflow automates the process of job posting management and candidate resume evaluation using LLM (Large Language Model) technology and PostgreSQL database integration.

## Structure
The repository consists of a single JSON workflow file that defines the entire n8n automation process.

## Workflow Components

### Main Files
- **recruiter-workflow.json**: The complete n8n workflow definition that handles job posting creation and resume screening.

## Workflow Architecture

### Job Posting Flow
1. **Webhook: Job posting** - Accepts POST requests with job details and optional file uploads
2. **File Processing** - Conditionally extracts text from uploaded files using Apache Tika
3. **LLM Integration** - Uses OpenAI API to structure job requirements and create screening criteria
4. **Database Storage** - Stores job postings in PostgreSQL with structured workflow JSON

### Resume Screening Flow
1. **Webhook: Resume upload** - Accepts candidate details and resume files
2. **Text Extraction** - Processes resume files through Apache Tika
3. **Job Matching** - Retrieves associated job posting from database
4. **Candidate Evaluation** - Uses LLM to score candidates against job requirements

## External Dependencies

### Services
- **n8n** - Workflow automation platform that runs this workflow
- **Apache Tika** - Used for text extraction from PDF/DOCX/TXT files
- **OpenAI API** - Provides LLM capabilities for job analysis and candidate scoring
- **PostgreSQL** - Database for storing job postings and candidate information

### API Credentials
- **OpenAI API** - Required for LLM functionality
- **PostgreSQL** - Required for database operations
- **Tika Basic Auth** - Optional for Apache Tika authentication

## Database Schema
The workflow uses a PostgreSQL database with tables including:
- **job_postings** - Stores job details, requirements, and workflow configuration
- **candidates** - Stores candidate information and evaluation results

## Usage & Operations
The workflow exposes two main webhook endpoints:
- **/recruiting/job-postings** - For creating new job postings
- **/recruiting/resumes** - For submitting candidate resumes

## Configuration
The workflow supports several environment variables:
- **TIKA_URL** - URL for Apache Tika server (default: http://tika:9998/tika)
- **OPENAI_MODEL** - OpenAI model to use (default: gpt-4.1-mini)
- **PUBLIC_BASE_URL** - Base URL for generating workflow links