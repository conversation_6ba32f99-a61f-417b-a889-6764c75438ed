{"nodes": [{"parameters": {"httpMethod": "POST", "path": "job-posting", "options": {"rawBody": true}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-96, 104], "id": "506ba44d-3650-4fbb-be69-f80e76af77e4", "name": "Job Posting Webhook", "webhookId": "job-posting"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "loose", "version": 1}, "conditions": [{"leftValue": "={{ Object.keys($json.files || {}).length }}", "rightValue": 0, "operator": {"type": "number", "operation": "gt"}, "id": "c968b253-e32d-4ac7-9338-d98dc3048aa3"}], "combinator": "and"}, "options": {"looseTypeValidation": true}}, "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [128, 104], "id": "e879ae92-db76-4605-9674-717523883c18", "name": "Check File Upload"}, {"parameters": {"operation": "text", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [352, 32], "id": "2d8b9bdf-eb10-49e4-ab5b-a492687ad140", "name": "Extract Job Description"}, {"parameters": {"assignments": {"assignments": [{"id": "job_title", "name": "job_title", "value": "={{ $('Job Posting Webhook').item.json.body.job_title || 'Untitled Position' }}", "type": "string"}, {"id": "experience_level", "name": "experience_level", "value": "={{ $('Job Posting Webhook').item.json.body.experience_level || 'Mid' }}", "type": "string"}, {"id": "years_range", "name": "years_range", "value": "={{ $('Job Posting Webhook').item.json.body.years_range || '2-5' }}", "type": "string"}, {"id": "roles_responsibilities", "name": "roles_responsibilities", "value": "={{ $('Job Posting Webhook').item.json.body.roles_responsibilities || '' }}", "type": "string"}, {"id": "keywords", "name": "keywords", "value": "={{ $('Job Posting Webhook').item.json.body.keywords || '' }}", "type": "string"}, {"id": "behaviours", "name": "behaviours", "value": "={{ $('Job Posting Webhook').item.json.body.behaviours || '' }}", "type": "string"}, {"id": "uploaded_content", "name": "uploaded_content", "value": "={{ $nodeExecutionOrder.includes('Extract Job Description') ? $('Extract Job Description').item.json.text : '' }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [640, 104], "id": "c8c3b69e-3f3d-451a-b6c3-aa9fd15e5de1", "name": "Prepare Job Data"}, {"parameters": {"promptType": "define", "text": "=Job Title: {{ $json.job_title }}\nExperience Level: {{ $json.experience_level }}\nYears of Experience: {{ $json.years_range }}\nRoles & Responsibilities: {{ $json.roles_responsibilities }}\nKeywords: {{ $json.keywords }}\nBehaviours: {{ $json.behaviours }}\n{% if $json.uploaded_content %}Uploaded Job Description: {{ $json.uploaded_content }}{% endif %}", "hasOutputParser": true, "options": {"systemMessage": "You are an expert HR consultant. Generate a comprehensive job analysis workflow JSON based on the provided job posting information. Create structured requirements and evaluation criteria.\n\nYou MUST respond with valid JSON that matches this structure:\n\n{\n  \"job_summary\": \"Brief description of the role\",\n  \"core_requirements\": [\n    \"List of essential technical and soft skills\"\n  ],\n  \"preferred_qualifications\": [\n    \"Nice-to-have skills and experience\"\n  ],\n  \"evaluation_criteria\": {\n    \"technical_skills\": {\n      \"weight\": 40,\n      \"description\": \"Technical competency assessment\"\n    },\n    \"experience\": {\n      \"weight\": 30,\n      \"description\": \"Relevant work experience\"\n    },\n    \"cultural_fit\": {\n      \"weight\": 20,\n      \"description\": \"Alignment with company values\"\n    },\n    \"communication\": {\n      \"weight\": 10,\n      \"description\": \"Communication and collaboration skills\"\n    }\n  },\n  \"red_flags\": [\n    \"Warning signs to watch for\"\n  ],\n  \"screening_questions\": [\n    \"Key questions for candidate evaluation\"\n  ]\n}"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [928, 104], "id": "7b28bfee-5d54-4cf7-a91a-354c7eef5b5b", "name": "Generate Job Workflow"}, {"parameters": {"schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"job_summary\": {\n      \"type\": \"string\",\n      \"description\": \"Brief description of the role\"\n    },\n    \"core_requirements\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      },\n      \"description\": \"List of essential technical and soft skills\"\n    },\n    \"preferred_qualifications\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      },\n      \"description\": \"Nice-to-have skills and experience\"\n    },\n    \"evaluation_criteria\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"technical_skills\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"weight\": {\n              \"type\": \"number\"\n            },\n            \"description\": {\n              \"type\": \"string\"\n            }\n          }\n        },\n        \"experience\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"weight\": {\n              \"type\": \"number\"\n            },\n            \"description\": {\n              \"type\": \"string\"\n            }\n          }\n        },\n        \"cultural_fit\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"weight\": {\n              \"type\": \"number\"\n            },\n            \"description\": {\n              \"type\": \"string\"\n            }\n          }\n        },\n        \"communication\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"weight\": {\n              \"type\": \"number\"\n            },\n            \"description\": {\n              \"type\": \"string\"\n            }\n          }\n        }\n      }\n    },\n    \"red_flags\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      },\n      \"description\": \"Warning signs to watch for\"\n    },\n    \"screening_questions\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      },\n      \"description\": \"Key questions for candidate evaluation\"\n    }\n  },\n  \"required\": [\n    \"job_summary\",\n    \"core_requirements\",\n    \"preferred_qualifications\",\n    \"evaluation_criteria\",\n    \"red_flags\",\n    \"screening_questions\"\n  ]\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.3, "position": [1064, 328], "id": "533f32f6-4eb3-4081-945d-199d3887b46b", "name": "Job Workflow Parser"}, {"parameters": {"httpMethod": "GET", "path": "resume-form", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-320, 524], "id": "resume-form-get-webhook", "name": "Resume Form GET", "webhookId": "resume-form-get"}, {"parameters": {"documentId": {"__rl": true, "value": "1xLkweU6QbBgb6LO8jmsRPd30k6nBufQWomZBKBz5YB0", "mode": "list", "cachedResultName": "resume_screening", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1xLkweU6QbBgb6LO8jmsRPd30k6nBufQWomZBKBz5YB0/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "job_postings", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1xLkweU6QbBgb6LO8jmsRPd30k6nBufQWomZBKBz5YB0/edit#gid=0"}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.7, "position": [-96, 524], "id": "get-job-titles-node", "name": "Get Job Titles", "credentials": {"googleSheetsOAuth2Api": {"id": "xopu35vIkCSzew32", "name": "Google Sheets account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "job_options", "name": "job_options", "value": "={{ $('Get Job Titles').all().map(item => `<option value=\"${item.json['job_id (UUID or serial)']}\">${item.json.job_title}</option>`).join('\\n                ') }}", "type": "string"}, {"id": "webhook_url", "name": "webhook_url", "value": "={{ $('Resume Form GET').item.json.webhookUrl.replace('resume-form', 'resume-submit') }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [128, 524], "id": "prepare-form-data", "name": "Prepare Form Data"}, {"parameters": {"assignments": {"assignments": [{"id": "form_html", "name": "form_html", "value": "=<!DOCTYPE html>\n<html>\n<head>\n    <title>Resume Upload for Job Application</title>\n    <style>\n        body { \n            font-family: Arial, sans-serif; \n            max-width: 600px; \n            margin: 50px auto; \n            padding: 20px; \n            background-color: #f5f5f5;\n        }\n        .form-container {\n            background-color: white;\n            padding: 30px;\n            border-radius: 8px;\n            box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n        }\n        .form-group { \n            margin-bottom: 20px; \n        }\n        label { \n            display: block; \n            margin-bottom: 5px; \n            font-weight: bold; \n            color: #333;\n        }\n        select, input[type=\"file\"] { \n            width: 100%; \n            padding: 12px; \n            border: 1px solid #ddd; \n            border-radius: 4px; \n            font-size: 14px;\n            box-sizing: border-box;\n        }\n        select:focus, input[type=\"file\"]:focus {\n            outline: none;\n            border-color: #007bff;\n            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);\n        }\n        button { \n            background-color: #007bff; \n            color: white; \n            padding: 12px 24px; \n            border: none; \n            border-radius: 4px; \n            cursor: pointer; \n            font-size: 16px;\n            width: 100%;\n        }\n        button:hover { \n            background-color: #0056b3; \n        }\n        .required { \n            color: red; \n        }\n        h1 {\n            color: #333;\n            margin-bottom: 10px;\n        }\n        p {\n            color: #666;\n            margin-bottom: 30px;\n        }\n    </style>\n</head>\n<body>\n    <div class=\"form-container\">\n        <h1>Resume Upload for Job Application</h1>\n        <p>Submit your resume for a specific job position</p>\n        <form action=\"{{ $json.webhook_url }}\" method=\"post\" enctype=\"multipart/form-data\">\n            <div class=\"form-group\">\n                <label for=\"job_position\">Job Position <span class=\"required\">*</span></label>\n                <select name=\"job_position\" id=\"job_position\" required>\n                    <option value=\"\">Select a job position...</option>\n                    {{ $json.job_options }}\n                </select>\n            </div>\n            <div class=\"form-group\">\n                <label for=\"resume\">Resume Upload <span class=\"required\">*</span></label>\n                <input type=\"file\" name=\"resume\" id=\"resume\" accept=\".pdf,.doc,.docx\" required>\n            </div>\n            <button type=\"submit\">Submit Application</button>\n        </form>\n    </div>\n</body>\n</html>", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [352, 524], "id": "prepare-form-html", "name": "Prepare Form HTML"}, {"parameters": {"respondWith": "text", "responseBody": "={{ $json.form_html }}", "options": {"responseHeaders": {"entries": [{"name": "Content-Type", "value": "text/html"}]}}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [576, 524], "id": "respond-with-form", "name": "Respond with Form"}, {"parameters": {"httpMethod": "POST", "path": "resume-submit", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-320, 724], "id": "83392cba-94f2-4dcc-b8f4-71225817d28c", "name": "Resume Form Trigger", "webhookId": "resume-submit"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"leftValue": "={{ Object.keys($json.files || {}).length }}", "rightValue": 0, "operator": {"type": "number", "operation": "gt"}, "id": "9f8a66bd-70e5-4534-8ffc-95700df75e25"}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [128, 524], "id": "99094288-4294-4b00-b190-7315599f485f", "name": "Check Resume File"}, {"parameters": {"operation": "text", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [352, 400], "id": "da021330-fda0-438d-b2f9-fe827a4d3fb3", "name": "Extract Resume Text"}, {"parameters": {"promptType": "define", "text": "=Extract candidate information from this resume text:\n\n{{ $('Extract Resume Text').item.json.text }}\n\nPlease extract the following information and return it in JSON format:\n- candidate_name: Full name of the candidate\n- candidate_email: Email address of the candidate\n- years_of_experience: Estimated years of professional experience (as a number)\n- key_skills: Array of main technical and professional skills\n\nReturn only valid JSON in this exact structure:\n{\n  \"candidate_name\": \"string\",\n  \"candidate_email\": \"string\",\n  \"years_of_experience\": number,\n  \"key_skills\": [\"skill1\", \"skill2\", \"skill3\"]\n}", "hasOutputParser": true, "options": {"systemMessage": "You are an expert resume parser. Extract structured candidate information from resume text. Always return valid JSON matching the requested schema. If information is not found, use reasonable defaults: empty string for text fields, 0 for numbers, empty array for arrays."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [576, 400], "id": "65a2b567-0c02-401a-91ba-aecf9c71fbff", "name": "Extract Candidate Info"}, {"parameters": {"schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"candidate_name\": {\n      \"type\": \"string\"\n    },\n    \"candidate_email\": {\n      \"type\": \"string\"\n    },\n    \"years_of_experience\": {\n      \"type\": \"number\"\n    },\n    \"key_skills\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      }\n    }\n  },\n  \"required\": [\"candidate_name\", \"candidate_email\", \"years_of_experience\", \"key_skills\"]\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.3, "position": [648, 624], "id": "43992e7c-faf8-4e57-8f46-6c989a5db823", "name": "Candidate Info Parser"}, {"parameters": {"assignments": {"assignments": [{"id": "job_id", "name": "=job_id", "value": "={{ $('Resume Form Trigger').item.json.body.job_position }}", "type": "string"}, {"id": "candidate_name", "name": "candidate_name", "value": "={{ $('Extract Candidate Info').item.json.output.candidate_name }}", "type": "string"}, {"id": "candidate_email", "name": "candidate_email", "value": "={{ $('Extract Candidate Info').item.json.output.candidate_email }}", "type": "string"}, {"id": "years_of_experience", "name": "years_of_experience", "value": "={{ $('Extract Candidate Info').item.json.output.years_of_experience }}", "type": "number"}, {"id": "key_skills", "name": "key_skills", "value": "={{ $('Extract Candidate Info').item.json.output.key_skills }}", "type": "object"}, {"id": "resume_content", "name": "resume_content", "value": "={{ $('Extract Resume Text').item.json.text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [992, 524], "id": "4d3a2cb2-e190-4c2a-92b8-0339403983ca", "name": "Prepare Candidate Data"}, {"parameters": {"promptType": "define", "text": "=CANDIDATE RESUME:\n{{ $('Prepare Candidate Data').item.json.resume_content }}\n\nCANDIDATE INFO:\nName: {{ $('Prepare Candidate Data').item.json.candidate_name }}\nEmail: {{ $('Prepare Candidate Data').item.json.candidate_email }}\nYears of Experience: {{ $('Prepare Candidate Data').item.json.years_of_experience }}\nKey Skills: {{ JSON.stringify($('Prepare Candidate Data').item.json.key_skills) }}\n\nJOB POSTING:\nTitle: {{ $json.job_title }}\nExperience Level: {{ $json.experience_level }}\nYears Required: {{ $json.years_range }}\nRoles: {{ $json.roles_responsibilities }}\nKeywords: {{ $json.keywords }}\nBehaviours: {{ $json.behaviours }}\n\nJOB WORKFLOW:\n{{ JSON.stringify(JSON.parse($json.workflow_json), null, 2) }}", "hasOutputParser": true, "options": {"systemMessage": "You are an expert technical recruiter. Analyze the candidate's resume against the job requirements and workflow criteria. Provide a comprehensive screening evaluation with detailed keyword matching, role alignment, behavior assessment, and experience validation.\n\nScoring Guide:\n- 0-2: Completely unqualified\n- 3-4: Minimal relevance\n- 5-6: Moderate fit with potential\n- 7-8: Strong candidate, minor gaps\n- 9-10: Ideal match, exceeds requirements\n\nEnsure thorough analysis of:\n- Technical keyword matching against job requirements\n- Role/responsibility alignment with candidate experience\n- Behavioral traits and soft skills assessment\n- Years of experience validation against job requirements\n- Cultural fit indicators\n- Communication skills assessment based on resume quality\n\nYou MUST respond with valid JSON matching this structure:\n\n{\n  \"skills_alignment\": {\n    \"technical_skills\": {\n      \"score\": 7,\n      \"strengths\": [\"Specific technical strengths matching job keywords\"],\n      \"gaps\": [\"Missing technical skills from job requirements\"],\n      \"keyword_matches\": [\"Direct keyword matches found\"],\n      \"keyword_gaps\": [\"Required keywords not found\"]\n    },\n    \"experience_relevance\": {\n      \"score\": 6,\n      \"relevant_experience\": [\"Matching experience areas to job roles\"],\n      \"experience_gaps\": [\"Missing experience from job requirements\"],\n      \"years_assessment\": \"Comparison of candidate years vs required years\"\n    }\n  },\n  \"behavioral_assessment\": {\n    \"score\": 8,\n    \"demonstrated_behaviors\": [\"Behaviors shown in resume matching job requirements\"],\n    \"behavior_gaps\": [\"Required behaviors not evidenced\"]\n  },\n  \"cultural_fit\": {\n    \"score\": 8,\n    \"positive_indicators\": [\"Signs of good cultural fit\"],\n    \"concerns\": [\"Potential cultural fit issues\"]\n  },\n  \"communication_assessment\": {\n    \"score\": 7,\n    \"writing_quality\": \"Assessment of resume writing and presentation\",\n    \"presentation\": \"How well information is structured and communicated\"\n  },\n  \"red_flags\": [\n    \"Any concerning patterns, gaps, or issues identified\"\n  ],\n  \"overall_recommendation\": {\n    \"rating\": 7,\n    \"recommendation\": \"Hire|Interview|Maybe|Pass\",\n    \"reasoning\": \"Detailed justification including keyword analysis, role alignment, behavior fit, and experience validation\",\n    \"next_steps\": \"Suggested next steps in the screening process\",\n    \"confidence_level\": \"High|Medium|Low - confidence in this assessment\"\n  },\n  \"interview_focus_areas\": [\n    \"Key technical and behavioral areas to explore in interview\"\n  ],\n  \"screening_summary\": {\n    \"total_score\": 7.2,\n    \"key_strengths\": [\"Top 3-5 candidate strengths\"],\n    \"main_concerns\": [\"Top 3-5 areas of concern\"],\n    \"fit_percentage\": 72\n  }\n}"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [1504, 524], "id": "eefde1fd-8d2f-4b03-a95d-48babcfb44ee", "name": "Analyze Candidate"}, {"parameters": {"schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"skills_alignment\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"technical_skills\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"score\": {\"type\": \"number\"},\n            \"strengths\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}},\n            \"gaps\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}},\n            \"keyword_matches\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}},\n            \"keyword_gaps\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}}\n          }\n        },\n        \"experience_relevance\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"score\": {\"type\": \"number\"},\n            \"relevant_experience\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}},\n            \"experience_gaps\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}},\n            \"years_assessment\": {\"type\": \"string\"}\n          }\n        }\n      }\n    },\n    \"behavioral_assessment\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"score\": {\"type\": \"number\"},\n        \"demonstrated_behaviors\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}},\n        \"behavior_gaps\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}}\n      }\n    },\n    \"cultural_fit\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"score\": {\"type\": \"number\"},\n        \"positive_indicators\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}},\n        \"concerns\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}}\n      }\n    },\n    \"communication_assessment\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"score\": {\"type\": \"number\"},\n        \"writing_quality\": {\"type\": \"string\"},\n        \"presentation\": {\"type\": \"string\"}\n      }\n    },\n    \"red_flags\": {\n      \"type\": \"array\",\n      \"items\": {\"type\": \"string\"}\n    },\n    \"overall_recommendation\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"rating\": {\"type\": \"number\"},\n        \"recommendation\": {\"type\": \"string\", \"enum\": [\"Hire\", \"Interview\", \"Maybe\", \"Pass\"]},\n        \"reasoning\": {\"type\": \"string\"},\n        \"next_steps\": {\"type\": \"string\"},\n        \"confidence_level\": {\"type\": \"string\", \"enum\": [\"High\", \"Medium\", \"Low\"]}\n      }\n    },\n    \"interview_focus_areas\": {\n      \"type\": \"array\",\n      \"items\": {\"type\": \"string\"}\n    },\n    \"screening_summary\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"total_score\": {\"type\": \"number\"},\n        \"key_strengths\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}},\n        \"main_concerns\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}},\n        \"fit_percentage\": {\"type\": \"number\"}\n      }\n    }\n  },\n  \"required\": [\"skills_alignment\", \"behavioral_assessment\", \"cultural_fit\", \"communication_assessment\", \"red_flags\", \"overall_recommendation\", \"interview_focus_areas\", \"screening_summary\"]\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.3, "position": [1576, 748], "id": "1c8d289a-8f7e-4691-864d-031d11441f5a", "name": "Candidate Analysis Parser"}, {"parameters": {"content": "## Job Posting Workflow\n\nHandles job posting submissions with manual form data and optional file uploads. Generates structured job workflow using LLM and stores in Google Sheets.", "height": 320, "width": 1072, "color": 2}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-312, -56], "id": "f731909d-4ed5-4d51-82b7-fd2b3ba5fe3e", "name": "Job Posting Note"}, {"parameters": {"content": "## Resume Screening Workflow\n\nForm-based resume submission with job selection, auto-extraction of candidate details, comprehensive AI analysis with keyword matching and behavioral assessment, and structured results storage.", "height": 464, "width": 1104, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-312, 304], "id": "d49e8611-0f18-436e-b90d-87856a57aaec", "name": "Resume Screening Note"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [936, 328], "id": "140d60b5-8bf9-4cc6-a91a-143021a7aba1", "name": "Google Gemini Chat Model", "credentials": {"googlePalmApi": {"id": "FiB5WJTAddrJO9ID", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "1xLkweU6QbBgb6LO8jmsRPd30k6nBufQWomZBKBz5YB0", "mode": "list", "cachedResultName": "resume_screening", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1xLkweU6QbBgb6LO8jmsRPd30k6nBufQWomZBKBz5YB0/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "job_postings", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1xLkweU6QbBgb6LO8jmsRPd30k6nBufQWomZBKBz5YB0/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"job_id (UUID or serial)": "={{ $runIndex }}-{{ new Date().getTime() }}", "job_title": "={{ $('Prepare Job Data').item.json.job_title }}", "experience_level": "={{ $('Prepare Job Data').item.json.experience_level }}", "years_range": "={{ $('Prepare Job Data').item.json.years_range }}", "roles_responsibilities": "={{ $('Prepare Job Data').item.json.roles_responsibilities }}", "keywords": "{{ $('Prepare Job Data').item.json.keywords }}", "behaviours": "={{ $('Prepare Job Data').item.json.behaviours }}", "uploaded_content": "={{ $('Prepare Job Data').item.json.uploaded_content }}", "workflow_json": "={{ JSON.stringify($('Generate Job Workflow').item.json.output) }}", "created_at (timestamp)": "={{ new Date().toISOString() }}"}, "matchingColumns": [], "schema": [{"id": "job_id (UUID or serial)", "displayName": "job_id (UUID or serial)", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "job_title", "displayName": "job_title", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "experience_level", "displayName": "experience_level", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "years_range", "displayName": "years_range", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "roles_responsibilities", "displayName": "roles_responsibilities", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "keywords", "displayName": "keywords", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "behaviours", "displayName": "behaviours", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "uploaded_content", "displayName": "uploaded_content", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "workflow_json", "displayName": "workflow_json", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "created_at (timestamp)", "displayName": "created_at (timestamp)", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.7, "position": [1280, 104], "id": "123cdebc-cf27-4363-b11b-cd9bc59f03ba", "name": "Append row in sheet", "credentials": {"googleSheetsOAuth2Api": {"id": "xopu35vIkCSzew32", "name": "Google Sheets account"}}}, {"parameters": {"documentId": {"__rl": true, "value": "1xLkweU6QbBgb6LO8jmsRPd30k6nBufQWomZBKBz5YB0", "mode": "list", "cachedResultName": "resume_screening", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1xLkweU6QbBgb6LO8jmsRPd30k6nBufQWomZBKBz5YB0/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "job_postings", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1xLkweU6QbBgb6LO8jmsRPd30k6nBufQWomZBKBz5YB0/edit#gid=0"}, "filtersUI": {"values": [{"lookupColumn": "job_id (UUID or serial)", "lookupValue": "={{ $('Resume Form Trigger').item.json.body.job_position }}"}]}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.7, "position": [1280, 524], "id": "277171cc-4226-46d8-b7e7-6ce6b635c03e", "name": "Get Job post", "credentials": {"googleSheetsOAuth2Api": {"id": "xopu35vIkCSzew32", "name": "Google Sheets account"}}}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "1xLkweU6QbBgb6LO8jmsRPd30k6nBufQWomZBKBz5YB0", "mode": "list", "cachedResultName": "resume_screening", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1xLkweU6QbBgb6LO8jmsRPd30k6nBufQWomZBKBz5YB0/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": **********, "mode": "list", "cachedResultName": "candidate_screening", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1xLkweU6QbBgb6LO8jmsRPd30k6nBufQWomZBKBz5YB0/edit#gid=**********"}, "columns": {"mappingMode": "defineBelow", "value": {"screening_id (UUID or serial)": "={{ $runIndex }}-{{ new Date().getTime() }}", "job_id (foreign key → job_postings)": "={{ $('Resume Form Trigger').item.json.body.job_position }}", "candidate_name": "={{ $('Prepare Candidate Data').item.json.candidate_name }}", "candidate_email": "={{ $('Prepare Candidate Data').item.json.candidate_email }}", "experience_years": "={{ $('Prepare Candidate Data').item.json.years_of_experience }}", "skills": "={{ JSON.stringify($('Prepare Candidate Data').item.json.key_skills) }}", "screening_notes": "={{ JSON.stringify($('Analyze Candidate').item.json.output) }}", "resume_link": "", "created_at (timestamp)": "={{ new Date().toISOString() }}"}, "matchingColumns": [], "schema": [{"id": "screening_id (UUID or serial)", "displayName": "screening_id (UUID or serial)", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "job_id (foreign key → job_postings)", "displayName": "job_id (foreign key → job_postings)", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "candidate_name", "displayName": "candidate_name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "candidate_email", "displayName": "candidate_email", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "experience_years", "displayName": "experience_years", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "skills", "displayName": "skills", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "screening_notes", "displayName": "screening_notes", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "resume_link", "displayName": "resume_link", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "created_at (timestamp)", "displayName": "created_at (timestamp)", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.7, "position": [1856, 524], "id": "32f2947b-7443-4f21-a578-7425b85d56fb", "name": "Append row in sheet1", "credentials": {"googleSheetsOAuth2Api": {"id": "xopu35vIkCSzew32", "name": "Google Sheets account"}}}], "connections": {"Resume Form GET": {"main": [[{"node": "Get Job Titles", "type": "main", "index": 0}]]}, "Get Job Titles": {"main": [[{"node": "Prepare Form Data", "type": "main", "index": 0}]]}, "Prepare Form Data": {"main": [[{"node": "Prepare Form HTML", "type": "main", "index": 0}]]}, "Prepare Form HTML": {"main": [[{"node": "Respond with Form", "type": "main", "index": 0}]]}, "Job Posting Webhook": {"main": [[{"node": "Check File Upload", "type": "main", "index": 0}]]}, "Check File Upload": {"main": [[{"node": "Extract Job Description", "type": "main", "index": 0}], [{"node": "Prepare Job Data", "type": "main", "index": 0}]]}, "Extract Job Description": {"main": [[{"node": "Prepare Job Data", "type": "main", "index": 0}]]}, "Prepare Job Data": {"main": [[{"node": "Generate Job Workflow", "type": "main", "index": 0}]]}, "Generate Job Workflow": {"main": [[{"node": "Append row in sheet", "type": "main", "index": 0}]]}, "Job Workflow Parser": {"ai_outputParser": [[{"node": "Generate Job Workflow", "type": "ai_outputParser", "index": 0}]]}, "Resume Form Trigger": {"main": [[{"node": "Check Resume File", "type": "main", "index": 0}]]}, "Check Resume File": {"main": [[{"node": "Extract Resume Text", "type": "main", "index": 0}], [{"node": "Prepare Candidate Data", "type": "main", "index": 0}]]}, "Extract Resume Text": {"main": [[{"node": "Extract Candidate Info", "type": "main", "index": 0}]]}, "Extract Candidate Info": {"main": [[{"node": "Prepare Candidate Data", "type": "main", "index": 0}]]}, "Candidate Info Parser": {"ai_outputParser": [[{"node": "Extract Candidate Info", "type": "ai_outputParser", "index": 0}]]}, "Prepare Candidate Data": {"main": [[{"node": "Get Job post", "type": "main", "index": 0}]]}, "Analyze Candidate": {"main": [[{"node": "Append row in sheet1", "type": "main", "index": 0}]]}, "Candidate Analysis Parser": {"ai_outputParser": [[{"node": "Analyze Candidate", "type": "ai_outputParser", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Analyze Candidate", "type": "ai_languageModel", "index": 0}, {"node": "Generate Job Workflow", "type": "ai_languageModel", "index": 0}, {"node": "Extract Candidate Info", "type": "ai_languageModel", "index": 0}]]}, "Get Job post": {"main": [[{"node": "Analyze Candidate", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"instanceId": "1e6cd82098a72c33f7cc889e64cfdec5945d7622af5151688c538bb863c02da0"}}